import React from 'react';
import PropTypes from 'prop-types';
import {
    PageDialog,
    Paper,
    Heading,
    Text,
    Flex,
    Grid,
    Box,
    InputCheckbox,
    Button,
    Paragraph,
    GridItem,
} from '@majoo-ui/react';
import { CircleInfoFilled } from '@majoo-ui/icons';
import moment from 'moment';
import { useAccountInfo } from '../../../AccountInfoContext';

const Row = ({ label, value }) => (
    <Grid columns={6} flow="row" gapX={1}>
        <GridItem
            colSpan={1}
            css={{
                padding: '$spacing-03',
                backgroundColor: '$gray100',
            }}
        >
            <Paragraph color="primary" paragraph="shortContentBold">
                {label}
            </Paragraph>
        </GridItem>
        <GridItem
            colSpan={5}
            css={{
                padding: '$spacing-03',
                backgroundColor: '$gray100',
            }}
        >
            <Paragraph color="primary">{value}</Paragraph>
        </GridItem>
    </Grid>
);

const DeleteTransactionPreviewModal = ({ isOpen, onOpenChange, data, onConfirm }) => {
    const { t, isMobile } = useAccountInfo();

    const formatDT = dt => (dt ? moment(dt).format('DD MMMM YYYY') : '-');

    return (
        <PageDialog open={isOpen} onOpenChange={onOpenChange} isMobile={isMobile}>
            <PageDialog.Title>{t('deleteTransaction.preview.title', 'Penghapusan Data')}</PageDialog.Title>
            <PageDialog.Content wrapperSize="md">
                <Paper responsive css={{ display: 'flex', flexDirection: 'column', gap: '$spacing-06' }}>
                    <Heading heading="pageTitle">{t('deleteTransaction.preview.detailTitle', 'Detail Data')}</Heading>

                    <Flex
                        align="start"
                        gap={5}
                        css={{
                            backgroundColor: '$gray100',
                            padding: '$spacing-03 $spacing-05',
                            borderRadius: '$lg',
                        }}
                    >
                        <CircleInfoFilled />
                        <Text color="secondary" css={{ flex: 1 }}>
                            {t(
                                'deleteTransaction.preview.infoMessage',
                                'Anda masih dapat melakukan pembatalan hingga maksimal 5 menit sebelum waktu eksekusi',
                            )}
                        </Text>
                    </Flex>

                    <Flex direction="column" gap={2}>
                        <Row
                            label={t('deleteTransaction.preview.outletName', 'Nama Outlet')}
                            value={data?.outletName || '-'}
                        />
                        <Row
                            label={t('deleteTransaction.preview.ownerEmail', 'Email Owner')}
                            value={data?.email || '-'}
                        />
                        <Row
                            label={t('deleteTransaction.preview.dateRange', 'Rentang Tanggal')}
                            value={
                                data?.startDate && data?.endDate
                                    ? `${formatDT(moment(data.startDate).startOf('day'))} - ${formatDT(data.endDate)}`
                                    : '-'
                            }
                        />
                        <Row
                            label={t('deleteTransaction.preview.approvedAt', 'Tanggal & Jam Persetujuan')}
                            value={moment().format('DD MMMM YYYY, HH:mm')}
                        />
                        <Row
                            label={t('deleteTransaction.preview.executedAt', 'Tanggal & Jam Eksekusi')}
                            value={moment().add(1, 'day').startOf('day').format('DD MMMM YYYY, HH:mm')}
                        />
                    </Flex>

                    <Box>
                        <Text css={{ fontWeight: 600, mb: '$spacing-03' }} color="primary">
                            {t('deleteTransaction.preview.confirmationLabel', 'Konfirmasi:')}
                        </Text>
                        <Flex direction="column" gap={3}>
                            <InputCheckbox
                                disabled
                                checked
                                label={
                                    <Text color="primary">
                                        {t(
                                            'deleteTransaction.cb1',
                                            'Saya memahami bahwa semua transaksi dan data terkait akan dihapus secara permanen.',
                                        )}
                                    </Text>
                                }
                            />
                            <InputCheckbox
                                disabled
                                checked
                                label={
                                    <Text color="primary">
                                        {t('deleteTransaction.cb2', 'Saya telah mencadangkan data yang diperlukan.')}
                                    </Text>
                                }
                            />
                            <InputCheckbox
                                disabled
                                checked
                                label={
                                    <Text color="primary">
                                        {t(
                                            'deleteTransaction.cb3',
                                            'Saya setuju untuk melanjutkan proses penghapusan.',
                                        )}
                                    </Text>
                                }
                            />
                        </Flex>
                    </Box>
                </Paper>
            </PageDialog.Content>
            <PageDialog.Footer css={{ display: 'flex', justifyContent: 'center' }}>
                <Flex css={{ width: '$full', '@lg': { width: 980, marginLeft: '320px' } }} justify="end" gap={3}>
                    <Button
                        buttonType="ghost"
                        onClick={() => onOpenChange(false)}
                        css={{ width: isMobile ? '$full' : 'auto', color: 'black' }}
                    >
                        {t('deleteTransaction.preview.back', 'Kembali')}
                    </Button>
                    <Button
                        buttonType="negative"
                        onClick={() => {
                            if (onConfirm) onConfirm();
                        }}
                        css={{ width: isMobile ? '$full' : 'auto' }}
                    >
                        Ajukan Hapus
                    </Button>
                </Flex>
            </PageDialog.Footer>
        </PageDialog>
    );
};

DeleteTransactionPreviewModal.propTypes = {
    isOpen: PropTypes.bool,
    onOpenChange: PropTypes.func,
    data: PropTypes.shape({
        outletName: PropTypes.string,
        email: PropTypes.string,
        startDate: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
        endDate: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
        approvalAt: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
        executionAt: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
        agree1: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
        agree2: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
        agree3: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
    }),
    onConfirm: PropTypes.func,
};

DeleteTransactionPreviewModal.defaultProps = {
    isOpen: false,
    onOpenChange: () => {},
    data: {},
    onConfirm: () => {},
};

export default DeleteTransactionPreviewModal;
