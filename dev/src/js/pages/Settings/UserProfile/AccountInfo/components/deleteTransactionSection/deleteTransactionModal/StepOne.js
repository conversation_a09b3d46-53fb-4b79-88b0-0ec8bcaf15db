import React, { useMemo } from 'react';
import {
    Paper,
    Heading,
    Text,
    FormGroup,
    FormLabel,
    InputSelect,
    InputDatePicker,
    Separator,
    Flex,
} from '@majoo-ui/react';
import { useFormContext, Controller, useWatch } from 'react-hook-form';
import moment from 'moment';
import { useAccountInfo } from '../../../AccountInfoContext';
import { CircleInfoFilled } from '@majoo-ui/icons';

const StepOne = () => {
    const { t, isMobile, listOutlet } = useAccountInfo();
    const {
        setValue,
        getValues,
        control,
        formState: { errors },
    } = useFormContext();

    const endDateWatcher = useWatch({
        control,
        name: 'endDate',
    });

    const outletOptions = useMemo(
        () =>
            listOutlet
                .filter(outlet => !!outlet.id_cabang)
                .map(outlet => ({ value: outlet.id_cabang, name: outlet.cabang_name })),
        [listOutlet],
    );

    return (
        <Paper responsive css={{ display: 'flex', flexDirection: 'column', gap: '$spacing-04' }}>
            <Heading as="h4" heading="pageTitle">
                {t('deleteTransaction.confirmTitle')}
            </Heading>
            <Text color="secondary">{t('deleteTransaction.featureInfo')}</Text>
            <ul style={{ marginTop: 0, paddingLeft: '1.5rem' }}>
                <li>
                    <Text color="secondary">{t('deleteTransaction.feature1')}</Text>
                </li>
                <li>
                    <Text color="secondary">{t('deleteTransaction.feature2')}</Text>
                </li>
            </ul>
            <Text color="secondary">{t('deleteTransaction.outletDeleteInfo')}</Text>

            <Controller
                name="outletId"
                control={control}
                rules={{ required: t('validation:form.required') }}
                render={({ field: { onChange, value } }) => (
                    <Flex direction="column" gap={3}>
                        <Text variant="label" color="primary">
                            {t('deleteTransaction.selectOutlet')}
                        </Text>
                        <InputSelect
                            option={outletOptions}
                            value={outletOptions.find(x => String(x.value) === String(value))}
                            placeholder={t('translation:placeholder.select', 'Pilih')}
                            onChange={({ value: v }) => onChange(v)}
                            isInvalid={!!errors.outletId}
                        />
                    </Flex>
                )}
            />

            <Flex
                align="start"
                gap={5}
                css={{
                    backgroundColor: '$gray100',
                    margin: '$spacing-06 0',
                    padding: '$spacing-03 $spacing-05',
                    borderRadius: '$lg',
                }}
            >
                <CircleInfoFilled />
                <Text color="secondary" css={{ flex: 1 }}>
                    {t('deleteTransaction.dateRangeNote')}
                </Text>
            </Flex>

            <Flex direction={isMobile ? 'column' : 'row'} gap={5}>
                <Controller
                    name="startDate"
                    control={control}
                    render={({ field }) => (
                        <Flex direction="column" gap={3} css={{ flex: 1 }}>
                            <Text variant="label" color="primary">
                                {t('deleteTransaction.startDate')}
                            </Text>
                            <InputDatePicker
                                {...field}
                                key={endDateWatcher}
                                onChange={date => {
                                    field.onChange(date);
                                    if (moment(getValues('endDate')).diff(moment(date), 'days', true) > 30) {
                                        setValue('endDate', moment(date).add(30, 'day').startOf('day').toDate());
                                    }
                                }}
                                minDate={moment(endDateWatcher).subtract(30, 'day').startOf('day').toDate()}
                                maxDate={moment(endDateWatcher).endOf('day').toDate()}
                                size="lg"
                                isInvalid={!!errors.startDate}
                            />
                        </Flex>
                    )}
                />
                <Controller
                    name="endDate"
                    control={control}
                    render={({ field }) => (
                        <Flex direction="column" gap={3} css={{ flex: 1 }}>
                            <Text variant="label" color="primary">
                                {t('deleteTransaction.endDate')}
                            </Text>
                            <InputDatePicker
                                {...field}
                                size="lg"
                                maxDate={moment().subtract(3, 'days').startOf('day').toDate()}
                                isInvalid={!!errors.endDate}
                            />
                        </Flex>
                    )}
                />
            </Flex>

            <Separator />
            <Flex direction="column">
                <Text variant="caption">
                    <b>{t('translation:label.note', 'Catatan:')}</b> {t('deleteTransaction.caution1')}
                </Text>
                <ul style={{ marginTop: 0, paddingLeft: '1.5rem' }}>
                    <li>
                        <Text variant="caption">{t('deleteTransaction.caution2')}</Text>
                    </li>
                    <li>
                        <Text variant="caption">{t('deleteTransaction.caution3')}</Text>
                    </li>
                </ul>
            </Flex>
        </Paper>
    );
};

export default StepOne;
