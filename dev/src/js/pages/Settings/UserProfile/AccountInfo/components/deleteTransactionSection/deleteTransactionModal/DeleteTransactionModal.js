import React, { useState, useContext } from 'react';
import PropTypes from 'prop-types';
import { PageDialog, Flex, StepWizard, Button, AlertDialog, ToastContext } from '@majoo-ui/react';
import { useForm, FormProvider } from 'react-hook-form';
import moment from 'moment';
import { useAccountInfo } from '../../../AccountInfoContext';
import StepOne from './StepOne';
import StepTwo from './StepTwo';
import VerificationModal from '../../../components/verificationModal';
import * as employeeApi from '~/data/employee';
import { catchError, getNestedProperty } from '~/utils/helper';
import VerificationFailedModal from '../../verificationFailedModal';
import DeleteTransactionPreviewModal from './DeleteTransactionPreviewModal';
import { createDeleteTransaction, blockDeleteTransaction } from '~/data/users';

const navContents = t => [{ title: t('deleteTransaction.step1Title') }, { title: t('deleteTransaction.step2Title') }];

const DeleteTransactionModal = ({ isOpen, onOpenChange }) => {
    const { t, isMobile, formData, showProgress, hideProgress, listOutlet } = useAccountInfo();
    const { addToast } = useContext(ToastContext);

    const [sw, setSw] = useState({});
    const [completedSteps, setCompletedSteps] = useState([false, false]);
    const [isOpenAlert, setIsOpenAlert] = useState(false);

    // Authorization and Verification Modal states
    const [verificationModalOpen, setVerificationModalOpen] = useState(false);
    const [verifFailModalOpen, setVerifFailModalOpen] = useState(false);
    const [previewModal, setPreviewModal] = useState({
        open: false,
        previewData: {},
    });
    const [invalidAttempts, setInvalidAttempts] = useState(0);

    const form = useForm({
        mode: 'onChange',
        defaultValues: {
            step: 1,
            outletId: '',
            startDate: moment().startOf('month').subtract(3, 'day').toDate(),
            endDate: moment().subtract(3, 'day').startOf('day').toDate(),
            agree1: 0,
            agree2: 0,
            agree3: 0,
            code: null,
        },
        resolver: values => {
            const errors = {};
            const { outletId } = values;

            if (!outletId) {
                errors.outletId = {
                    type: 'required',
                    message: t('validation:form.required'),
                };
            }

            return {
                values,
                errors,
            };
        },
    });

    const {
        watch,
        setValue,
        getValues,
        handleSubmit,
        formState: { isValid },
    } = form;
    const step = watch('step');
    const agree1 = watch('agree1');
    const agree2 = watch('agree2');
    const agree3 = watch('agree3');

    // Request verification code for authorization
    const fetchRequestVerifCode = async (onSuccess, onError) => {
        showProgress();
        const verificationPayload = {
            type: 'authorize_purge_transaction',
            value: formData.email,
            send_to: 'email',
        };

        try {
            await employeeApi.requestVerificationCode(verificationPayload);
            if (onSuccess) onSuccess();
            setVerificationModalOpen(true);
        } catch (error) {
            if (onError) onError(error);
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
            const errMsg = getNestedProperty(error, 'message', '').toLowerCase();
            if (errMsg.includes('blocked')) {
                setVerifFailModalOpen(true);
            }
        } finally {
            hideProgress();
        }
    };

    // Verify the code entered by user
    const fetchVerifyData = async (code, onError) => {
        showProgress();
        try {
            await employeeApi.verifyCode({
                type: 'authorize_purge_transaction',
                code,
            });
            // Reset invalid attempts on successful verification
            setInvalidAttempts(0);
            setValue('code', code);
            const selectedOutlet = (listOutlet || []).find(o => String(o.id_cabang) === String(getValues('outletId')));
            setVerificationModalOpen(false);
            setPreviewModal({
                open: true,
                previewData: {
                    outletName: selectedOutlet ? selectedOutlet?.cabang_name : '-',
                    email: formData.email,
                    ...getValues(),
                },
            });
        } catch (error) {
            const errorMessage = catchError(error).toLowerCase();

            // Check if error message contains "invalid"
            if (errorMessage.includes('invalid')) {
                const newInvalidAttempts = invalidAttempts + 1;
                setInvalidAttempts(newInvalidAttempts);

                // If 3 consecutive invalid attempts, call blockDeleteTransaction
                if (newInvalidAttempts >= 3) {
                    try {
                        await blockDeleteTransaction({
                            type: 'authorize_purge_transaction',
                            value: formData.email,
                        });
                    } catch (blockError) {
                        // Handle block error if needed
                        console.error('Failed to block delete transaction:', blockError);
                    } finally {
                        setVerificationModalOpen(false);
                        setVerifFailModalOpen(true);
                        // Reset attempts after blocking
                        setInvalidAttempts(0);
                    }
                }
            } else {
                // Reset invalid attempts for non-invalid errors
                setInvalidAttempts(0);
            }

            if (onError) onError(error);
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const submitDeleteTransaction = async () => {
        showProgress();
        try {
            const formDataValues = getValues();
            const payload = {
                outlet_id: Number(formDataValues.outletId),
                start_date: moment(formDataValues.startDate).format('YYYY-MM-DD'),
                end_date: moment(formDataValues.endDate).format('YYYY-MM-DD'),
                code: formDataValues.code,
            };

            await createDeleteTransaction(payload);
            addToast({
                title: t('toast.success', { ns: 'translation' }),
                description: 'Pengajuan Hapus Transaksi berhasil diajukan',
                variant: 'success',
            });
            setPreviewModal({ open: false });
            onOpenChange(false);
        } catch (error) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const go = dir => {
        if (dir === 'back') {
            setValue('step', step - 1);
            sw.previousStep?.();
            return;
        }
        // next
        setCompletedSteps(prev => prev.map((_, idx) => idx < step));
        setValue('step', step + 1);
        sw.nextStep?.();
    };

    const FooterButtons = () => (
        <Flex justify="end" gap={3} css={{ flex: 1 }}>
            <Button
                type="button"
                buttonType="ghost"
                css={{ width: isMobile ? '$full' : 'auto' }}
                onClick={() => {
                    if (step === 1) setIsOpenAlert(true);
                    else go('back');
                }}
            >
                {t(step === 1 ? 'translation:label.cancel' : 'translation:label.back')}
            </Button>
            <Button
                type="button"
                css={{ width: isMobile ? '$full' : 'auto' }}
                disabled={(step === 1 && !isValid) || (step === 2 && !(agree1 && agree2 && agree3))}
                onClick={handleSubmit(data => (step === 1 ? go('next') : fetchRequestVerifCode()))}
            >
                {t(step === 1 ? 'translation:label.nextThen' : 'translation:label.save')}
            </Button>
        </Flex>
    );

    return (
        <React.Fragment>
            <PageDialog open={isOpen} onOpenChange={() => setIsOpenAlert(true)} isMobile={isMobile}>
                <PageDialog.Title>{t('deleteTransaction.title')}</PageDialog.Title>
                <PageDialog.Content wrapperSize="lg">
                    <FormProvider {...form}>
                        <form id="form-delete-transaction">
                            <StepWizard
                                completedSteps={completedSteps}
                                initialStep={1}
                                navContents={navContents(t)}
                                setSw={setSw}
                                forbidClickNav
                                isMobile={isMobile}
                            >
                                <StepOne />
                                <StepTwo />
                            </StepWizard>
                        </form>
                    </FormProvider>

                    <AlertDialog
                        dialogType="negative"
                        isMobile={isMobile}
                        open={isOpenAlert}
                        title={t('translation:label.cancelled', 'Batal')}
                        description={t('deleteTransaction.alertCancel')}
                        labelConfirm={t('translation:label.continue')}
                        labelCancel={t('translation:label.cancel')}
                        onCancel={() => setIsOpenAlert(false)}
                        onConfirm={() => onOpenChange(false)}
                    />
                </PageDialog.Content>

                <PageDialog.Footer css={{ display: 'flex', justifyContent: 'center' }}>
                    <Flex
                        css={{
                            width: '$full',
                            maxWidth: 1318,
                            margin: '0 auto',
                        }}
                    >
                        <FooterButtons />
                    </Flex>
                </PageDialog.Footer>
            </PageDialog>
            {/* Verification Modal */}
            {verificationModalOpen && (
                <VerificationModal
                    isMobile={isMobile}
                    isOpen={verificationModalOpen}
                    onOpenChange={setVerificationModalOpen}
                    verificationType="email"
                    contactValue={formData.email}
                    requestVerificationCode={async (_type, _contactValue, onSuccess, onError) => {
                        await fetchRequestVerifCode(onSuccess, onError);
                    }}
                    onSubmit={(code, _, onError) => fetchVerifyData(code, onError)}
                    t={t}
                    maxLength={6}
                />
            )}
            {verifFailModalOpen && (
                <VerificationFailedModal
                    isOpen={verifFailModalOpen}
                    onOpenChange={open => {
                        if (!open) {
                            setVerifFailModalOpen(false);
                            onOpenChange(false);
                        }
                    }}
                    t={t}
                />
            )}
            {previewModal.open && (
                <DeleteTransactionPreviewModal
                    isOpen={previewModal.open}
                    onOpenChange={setPreviewModal}
                    data={previewModal.previewData}
                    onConfirm={submitDeleteTransaction}
                />
            )}
        </React.Fragment>
    );
};

DeleteTransactionModal.propTypes = {
    isOpen: PropTypes.bool,
    onOpenChange: PropTypes.func,
};

DeleteTransactionModal.defaultProps = {
    isOpen: false,
    onOpenChange: () => {},
};

export default DeleteTransactionModal;
