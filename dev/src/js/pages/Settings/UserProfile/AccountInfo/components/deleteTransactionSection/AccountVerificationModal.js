import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
    ModalDialog,
    ModalDialogTitle,
    ModalDialogContent,
    Button,
    ModalDialogFooter,
    DialogClose,
    Text,
    FormGroup,
    FormLabel,
    Box,
    InputText,
} from '@majoo-ui/react';
import { EyeOutline, EyeSlashOutline } from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';
// import Turnstile, { useTurnstile } from 'react-turnstile';
import { useAccountInfo } from '../../AccountInfoContext';
import { catchError } from '../../../../../../utils/helper';
import { verifyDeleteTransactionAccount } from '../../../../../../data/users';

const AccountVerificationModal = ({ isOpen, onOpenChange, onVerifSuccess, type }) => {
    const { t, isMobile, showProgress, hideProgress, addToast, formData, idCabang } = useAccountInfo();
    const [isShowPassword, setShowPassword] = useState(false);
    // const [isTurnstileLoaded, setIsTurnstileLoaded] = useState(false);
    const [password, setPassword] = useState('');
    // const [captchaToken, setCaptchaToken] = useState(null);

    // const turnstile = useTurnstile();

    const handleVerifPassword = async () => {
        showProgress();

        // if (!captchaToken) {
        //     addToast({
        //         title: 'Error!',
        //         variant: 'failed',
        //         description: 'Recaptcha token has not been success generated',
        //     });
        //     hideProgress();
        //     return;
        // }

        try {
            const payload = {
                outlet_id: idCabang,
                email: formData.email,
                password,
            };
            const res = await verifyDeleteTransactionAccount(payload);
            if (!res.status) throw new Error(res.msg);
            // turnstile.reset();
            hideProgress();
            onVerifSuccess();
        } catch (error) {
            addToast({
                title: t('translation:toast.error'),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            // turnstile.reset();
            hideProgress();
        }
    };

    // const isButtonDisabled = !password || !isTurnstileLoaded || !captchaToken;
    const isButtonDisabled = !password;

    const getButtonText = () => {
        // if (!isTurnstileLoaded || (!captchaToken && isTurnstileLoaded)) {
        //     return t('label.pleaseWait', { ns: 'translation' }, 'Mohon Tunggu Sebentar');
        // }
        return t('translation:label.continue2', 'Lanjutkan');
    };

    // useEffect(() => {
    //     const checkTurnstileReady = () => {
    //         if (window.turnstile) {
    //             setIsTurnstileLoaded(true);
    //         } else {
    //             setTimeout(checkTurnstileReady, 100);
    //         }
    //     };
    //     checkTurnstileReady();
    // }, []);

    return (
        <React.Fragment>
            <ModalDialog size="md" open={isOpen} onOpenChange={onOpenChange} isMobile={isMobile}>
                <ModalDialogTitle>
                    {type === 'cancel' ? 'Batalkan Penghapusan Transaksi' : 'Pengajuan Hapus Transaksi'}
                </ModalDialogTitle>
                <ModalDialogContent>
                    <Text color="primary">
                        {t('delete.modalDelete.desc', 'Untuk melanjutkan, majoo perlu mengonfirmasi kepemilikan akun')}
                    </Text>
                    <FormGroup css={{ mt: 16 }}>
                        <FormLabel htmlFor="password" css={{ color: '$textPrimary', fontWeight: 600 }}>
                            {t('delete.modalDelete.password', 'Masukkan Kata Sandi')}
                        </FormLabel>
                        <Box css={{ position: 'relative' }}>
                            <InputText
                                placeholder={t('translation:placeholder.example', 'Contoh: ')}
                                type={isShowPassword ? 'text' : 'password'}
                                value={password}
                                onChange={e => {
                                    setPassword(e.target.value);
                                }}
                            />
                            <Box
                                onClick={() => {
                                    setShowPassword(value => !value);
                                }}
                                css={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    cursor: 'pointer',
                                    position: 'absolute',
                                    right: 0,
                                    top: '-20px',
                                    height: 40,
                                    transform: 'translate(-50%, 50%)',
                                }}
                            >
                                {isShowPassword ? (
                                    <EyeOutline color={foundations.colors.iconSecondary} />
                                ) : (
                                    <EyeSlashOutline color={foundations.colors.iconSecondary} />
                                )}
                            </Box>
                        </Box>
                    </FormGroup>
                </ModalDialogContent>
                <ModalDialogFooter css={{ display: 'flex', gap: '$compact' }}>
                    <DialogClose asChild>
                        <Button aria-label="Close" buttonType="ghost" width={isMobile && '50%'}>
                            {t('translation:label.cancel')}
                        </Button>
                    </DialogClose>
                    <Button
                        type="button"
                        disabled={isButtonDisabled}
                        width={isMobile && '50%'}
                        onClick={() => handleVerifPassword()}
                    >
                        {getButtonText()}
                    </Button>
                </ModalDialogFooter>
            </ModalDialog>
            {/* <Turnstile
                refreshExpired="auto"
                sitekey={process.env.TURNSTILE_SITE_KEY}
                onVerify={token => {
                    setCaptchaToken(token);
                }}
                theme="light"
                appearance="interaction-only"
            /> */}
        </React.Fragment>
    );
};

AccountVerificationModal.propTypes = {
    isOpen: PropTypes.bool,
    onOpenChange: PropTypes.func,
    onVerifSuccess: PropTypes.func,
    type: PropTypes.string,
};

AccountVerificationModal.defaultProps = {
    isOpen: false,
    onOpenChange: () => {},
    onVerifSuccess: () => {},
    type: 'create',
};

export default AccountVerificationModal;
