import React, { useEffect, useMemo, useState } from 'react';
import { Flex, <PERSON><PERSON>, Button, Text } from '@majoo-ui/react';
import { useAccountInfo } from '../../AccountInfoContext';
import AccountVerificationModal from './AccountVerificationModal';
import DeleteTransactionModal from './deleteTransactionModal/DeleteTransactionModal';
import { getDeleteTransactionStatus, cancelDeleteTransaction } from '~/data/users';
import moment from 'moment';
import { catchError } from '~/utils/helper';

const DeleteTransactionSection = () => {
    const { t, isMobile, addToast, showProgress, hideProgress, i18n } = useAccountInfo();
    const [verifModal, setVerifModal] = useState({
        open: false,
        type: 'create',
    });
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);

    // delete-transaction status state
    const [deleteTxStatus, setDeleteTxStatus] = useState({
        id: null,
        queued_at: null,
        is_cancelable: false,
    });

    const fetchStatus = async () => {
        try {
            const res = await getDeleteTransactionStatus();
            if (res && res.status && res.data) {
                setDeleteTxStatus({
                    id: res.data.id || null,
                    queued_at: res.data.process_date || null,
                    is_cancelable: !!res.data.is_cancelable,
                });
            } else {
                setDeleteTxStatus({
                    id: null,
                    queued_at: null,
                    is_cancelable: false,
                });
            }
        } catch (e) {
            console.error({ e });
            // silently ignore; default state keeps create button visible
        }
    };

    const fetchCancelDeleteTransaction = async () => {
        showProgress();
        try {
            await cancelDeleteTransaction(deleteTxStatus.id);
            setVerifModal({
                open: false,
            });
            addToast({
                title: t('toast.success', { ns: 'translation' }),
                description: 'Pengajuan Hapus Transaksi berhasil dibatalkan',
                variant: 'failed',
            });
            await fetchStatus();
        } catch (error) {
            addToast({
                title: t('translation:toast.error'),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const scheduledMessage = useMemo(() => {
        if (!deleteTxStatus?.queued_at) return '';
        const dateStr = moment(deleteTxStatus.queued_at)
            .locale(i18n?.language || 'id')
            .format('DD MMMM YYYY, HH:mm');
        return `${t('deleteTransaction.title', 'Pengajuan Hapus Transaksi')} akan dilakukan pada ${dateStr} WIB.`;
    }, [deleteTxStatus?.queued_at, i18n?.language, t]);

    useEffect(() => {
        fetchStatus();
    }, []);

    return (
        <React.Fragment>
            <Flex direction={!isMobile ? 'row' : 'column'} justify="between" align="start" gap={3}>
                <Flex direction="column" gap={3}>
                    <Heading as="h3" heading="sectionTitle" css={{ '@sm': { mb: '$spacing-05 !important' } }}>
                        {t('deleteTransaction.title', 'Pengajuan Hapus Transaksi')}
                    </Heading>
                    <Text css={{ color: '$textDisable', width: '$full', '@lg': { width: 250 } }}>
                        {t(
                            'deleteTransaction.info',
                            'Proses hapus akun akan memutus semua integrasi pada platform majoo',
                        )}
                    </Text>
                </Flex>
                <Flex direction={!isMobile ? 'row' : 'column'} css={{ width: !isMobile ? '70%' : '100%' }} gap={4}>
                    {deleteTxStatus.id ? (
                        <Flex gap={5} align="center">
                            <Button
                                type="button"
                                buttonType="negative-secondary"
                                onClick={() => setVerifModal({ open: true, type: 'cancel' })}
                                size="sm"
                            >
                                {t('translation:label.cancelled', 'Batalkan')}
                            </Button>
                            {!!scheduledMessage && (
                                <Text color="primary" variant="caption">
                                    {scheduledMessage}
                                </Text>
                            )}
                        </Flex>
                    ) : (
                        <Button
                            type="button"
                            buttonType="negative-secondary"
                            onClick={() => {
                                setVerifModal({ open: true, type: 'create' });
                            }}
                            size="sm"
                        >
                            {t('deleteTransaction.button')}
                        </Button>
                    )}
                </Flex>
            </Flex>
            {verifModal.open && (
                <AccountVerificationModal
                    isOpen={verifModal.open}
                    type={verifModal.type}
                    onOpenChange={open => setVerifModal(current => ({ ...current, open }))}
                    onVerifSuccess={() => {
                        if (verifModal.type === 'create') {
                            setVerifModal({ open: false });
                            setDeleteModalOpen(true);
                        }
                        if (verifModal.type === 'cancel') {
                            fetchCancelDeleteTransaction();
                        }
                    }}
                />
            )}
            {deleteModalOpen && (
                <DeleteTransactionModal
                    isOpen={deleteModalOpen}
                    onOpenChange={open => {
                        setDeleteModalOpen(open);
                        if (!open) {
                            fetchStatus();
                        }
                    }}
                />
            )}
        </React.Fragment>
    );
};

export default DeleteTransactionSection;
